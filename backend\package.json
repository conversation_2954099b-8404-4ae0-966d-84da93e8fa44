{"name": "museart-backend", "version": "1.0.0", "description": "Backend for Museart application", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.1", "mysql2": "^3.14.1", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.0.1"}}