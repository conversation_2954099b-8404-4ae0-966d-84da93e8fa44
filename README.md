# 🎨 Museart - Museum Art Mobile Application

Museart là một ứng dụng di động hiện đại dành cho việc khám phá và tương tác với các tác phẩm nghệ thuật, triển lãm và bài viết nghệ thuật. Ứng dụng được xây dựng với React Native và Node.js, tích hợp AI và các tính năng thông minh.

## 📱 Tính năng chính

### 🎯 Core Features
- **Khám phá Tác phẩm Nghệ thuật**: Duyệt qua hàng nghìn tác phẩm nghệ thuật từ Art Institute of Chicago API
- **Chi tiết Tác phẩm**: <PERSON>em thông tin chi tiết, lịch sử, và mô tả của từng tác phẩm
- **Triển lãm**: <PERSON>h<PERSON><PERSON> phá các triển lãm nghệ thuật với thông tin đầy đủ
- **<PERSON><PERSON><PERSON> viết Nghệ thuật**: <PERSON><PERSON><PERSON> c<PERSON><PERSON> bài viết và tin tức về nghệ thuật
- **Hệ thống Xác thực**: Đăng ký, đăng nhập với JWT authentication
- **Chế độ Khách**: Truy cập ứng dụng mà không cần đăng ký

### 🤖 AI & Smart Features
- **Chatbot AI**: Trợ lý AI sử dụng Groq LLM để trả lời câu hỏi về nghệ thuật
- **Tìm kiếm bằng Hình ảnh**: Upload ảnh để tìm tác phẩm tương tự sử dụng CLIP model
- **Voice Search**: Tìm kiếm bằng giọng nói với Whisper STT
- **RAG System**: Retrieval-Augmented Generation cho câu trả lời chính xác

### 🛒 Shopping & Commerce
- **Giỏ hàng**: Thêm sản phẩm nghệ thuật vào giỏ hàng
- **Thanh toán**: Tích hợp VNPay payment gateway
- **Quản lý Đơn hàng**: Theo dõi và quản lý đơn hàng

### 💫 Social Features
- **Bookmark**: Lưu tác phẩm yêu thích
- **Reactions**: Like/Unlike tác phẩm và triển lãm
- **Comments**: Bình luận và thảo luận về tác phẩm

### 🎵 Multimedia
- **Audio Guide**: Nghe hướng dẫn âm thanh cho tác phẩm
- **Video Content**: Xem video giới thiệu về tác phẩm
- **Image Gallery**: Xem ảnh chất lượng cao của tác phẩm

## 🏗️ Kiến trúc Hệ thống

### Frontend (React Native + Expo)
```
frontend/
├── screens/           # Các màn hình chính
│   ├── artworks/     # Màn hình tác phẩm
│   ├── exhibitions/  # Màn hình triển lãm
│   ├── articles/     # Màn hình bài viết
│   ├── auth/         # Xác thực
│   └── shopping/     # Mua sắm
├── components/       # Components tái sử dụng
├── navigation/       # Điều hướng
├── context/          # Context API
├── store/           # Redux store
└── services/        # API services
```

### Backend (Node.js + Express)
```
backend/
├── controllers/     # Business logic
├── models/         # Database models (Sequelize)
├── routes/         # API routes
├── middleware/     # Authentication middleware
└── database/       # Database configuration
```

### AI Services (Python + FastAPI)
```
backend/
├── main.py         # FastAPI server
├── image_retrieval.py  # Image search
└── art_products/   # Vector database
```

## 🛠️ Công nghệ sử dụng

### Frontend
- **React Native 0.76.9** - Framework di động
- **Expo 52.0.0** - Development platform
- **React Navigation 7.x** - Điều hướng
- **Redux Toolkit** - State management
- **Axios** - HTTP client
- **NativeWind** - Styling với Tailwind CSS

### Backend
- **Node.js + Express** - Web server
- **Sequelize + MySQL** - Database ORM
- **JWT** - Authentication
- **bcryptjs** - Password hashing

### AI & ML
- **Python + FastAPI** - AI services
- **Groq LLM** - Large Language Model
- **LangChain** - LLM framework
- **ChromaDB** - Vector database
- **CLIP Model** - Image similarity
- **Whisper** - Speech-to-text
- **Ollama Embeddings** - Text embeddings

### External APIs
- **Art Institute of Chicago API** - Artwork data
- **VNPay** - Payment gateway

## 🚀 Cài đặt và Chạy

### Yêu cầu hệ thống
- Node.js 18+
- Python 3.8+
- MySQL 8.0+
- Expo CLI
- Android Studio / Xcode (cho development)

### 1. Clone Repository
```bash
git clone https://github.com/your-username/museart-mobile.git
cd museart-mobile
```

### 2. Cài đặt Backend
```bash
cd backend
npm install

# Tạo database MySQL
mysql -u root -p
CREATE DATABASE muse_art;

# Chạy backend
npm run dev
```

### 3. Cài đặt AI Services
```bash
cd backend
pip install -r requirements.txt

# Chạy AI server
python main.py
```

### 4. Cài đặt Frontend
```bash
cd frontend
npm install

# Chạy ứng dụng
npm start
```

### 5. Cấu hình Environment
Tạo file `.env` trong thư mục backend:
```env
GROQ_API_KEY=your_groq_api_key
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=muse_art
JWT_SECRET=your_jwt_secret
```

## 📱 Màn hình Ứng dụng

### 🎨 Artworks
- **Danh sách Tác phẩm**: Grid view với pagination
- **Chi tiết Tác phẩm**: Thông tin đầy đủ, hình ảnh HD
- **Tìm kiếm**: Text search, voice search, image search

### 🏛️ Exhibitions  
- **Danh sách Triển lãm**: Các triển lãm hiện tại và sắp tới
- **Chi tiết Triển lãm**: Thông tin, tác phẩm trong triển lãm

### 📰 Articles
- **Tin tức Nghệ thuật**: Bài viết và tin tức mới nhất
- **AI Summary**: Tóm tắt bài viết bằng AI

### 🛒 Shopping
- **Giỏ hàng**: Quản lý sản phẩm
- **Thanh toán**: VNPay integration

### 👤 Profile
- **Thông tin cá nhân**: Quản lý tài khoản
- **Bookmark**: Tác phẩm đã lưu
- **Lịch sử**: Hoạt động của người dùng

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/signup` - Đăng ký
- `POST /api/auth/signin` - Đăng nhập
- `POST /api/auth/signout` - Đăng xuất

### Artworks
- `GET /api/artwork/reactions/:id` - Lấy reactions
- `POST /api/artwork/reactions/:id` - Toggle reaction

### AI Services
- `POST /chat` - Chatbot AI
- `POST /image_search` - Tìm kiếm bằng hình ảnh
- `POST /speech-to-text` - Chuyển giọng nói thành text
- `POST /summary` - Tóm tắt bài viết

## 🎯 Roadmap

### Phase 1 (Completed)
- ✅ Basic CRUD operations
- ✅ Authentication system
- ✅ AI chatbot integration
- ✅ Image search functionality
- ✅ Voice search

### Phase 2 (In Progress)
- 🔄 VNPay payment integration
- 🔄 Enhanced UI/UX
- 🔄 Performance optimization

### Phase 3 (Planned)
- 📋 Push notifications
- 📋 Offline mode
- 📋 Social sharing
- 📋 Advanced analytics

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 👥 Team

- **Frontend Developer** - React Native, UI/UX
- **Backend Developer** - Node.js, Database
- **AI Engineer** - Machine Learning, NLP
- **DevOps** - Deployment, CI/CD

## 📞 Liên hệ

- Email: <EMAIL>
- Website: https://museart.com
- GitHub: https://github.com/museart-mobile

---

⭐ **Star this repository if you find it helpful!**
